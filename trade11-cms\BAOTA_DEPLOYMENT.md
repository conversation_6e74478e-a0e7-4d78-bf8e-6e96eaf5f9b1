# 宝塔面板部署指南 - Trade11 CMS

本指南将帮助您在宝塔面板上一键部署 Trade11 CMS (Strapi)。

## 前置要求

### 1. 服务器要求
- **操作系统**: CentOS 7+, Ubuntu 18.04+, Debian 9+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB 可用空间
- **CPU**: 最低 2 核心

### 2. 宝塔面板
- **版本**: 7.0+
- **已安装软件**: Nginx, MySQL 5.7+/8.0+, Node.js 18+, PM2

## 快速部署

### 1. 准备工作

```bash
# 1. 上传项目文件到服务器
# 将整个 trade11-cms 文件夹上传到 /www/wwwroot/

# 2. 设置执行权限
chmod +x /www/wwwroot/trade11-cms/deploy-bt.sh

# 3. 运行一键部署脚本
cd /www/wwwroot/trade11-cms
./deploy-bt.sh
```

### 2. 宝塔面板配置

#### 2.1 创建网站
1. 登录宝塔面板
2. 点击 "网站" → "添加站点"
3. 填写域名信息
4. 选择 "不创建FTP" 和 "不创建数据库"
5. 网站目录设置为: `/www/wwwroot/trade11-cms`

#### 2.2 配置 Nginx
1. 点击网站设置 → "配置文件"
2. 将 `nginx.conf` 中的配置复制到网站配置中
3. 修改 `server_name` 为您的域名
4. 保存并重载配置

#### 2.3 创建数据库
1. 点击 "数据库" → "添加数据库"
2. 数据库名: `trade11_cms`
3. 用户名: `trade11_cms_user`
4. 设置强密码并记录

#### 2.4 配置 SSL (推荐)
1. 点击网站设置 → "SSL"
2. 选择 "Let's Encrypt" 或上传自己的证书
3. 开启 "强制HTTPS"

### 3. 环境配置

#### 3.1 编辑环境变量
```bash
cd /www/wwwroot/trade11-cms
cp .env.example .env
nano .env
```

#### 3.2 必须修改的配置
```env
# 数据库配置
DATABASE_PASSWORD=your-database-password

# 安全密钥 (使用随机字符串)
APP_KEYS=random-key-1,random-key-2,random-key-3,random-key-4
API_TOKEN_SALT=random-salt-1
ADMIN_JWT_SECRET=random-secret-1
TRANSFER_TOKEN_SALT=random-salt-2
JWT_SECRET=random-secret-2

# CORS 配置 (添加您的前端域名)
CORS_ORIGIN=https://your-frontend-domain.com
```

#### 3.3 生成随机密钥
```bash
# 生成随机密钥的命令
openssl rand -base64 32
```

## 手动部署步骤

如果自动部署脚本失败，可以按照以下步骤手动部署：

### 1. 安装依赖
```bash
cd /www/wwwroot/trade11-cms
npm install --production
```

### 2. 构建项目
```bash
npm run build
```

### 3. 启动服务
```bash
# 使用 PM2 启动
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 管理命令

### PM2 管理
```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs trade11-cms

# 重启服务
pm2 restart trade11-cms

# 停止服务
pm2 stop trade11-cms

# 删除服务
pm2 delete trade11-cms
```

### 数据库管理
```bash
# 备份数据库
mysqldump -u trade11_cms_user -p trade11_cms > backup.sql

# 恢复数据库
mysql -u trade11_cms_user -p trade11_cms < backup.sql
```

## 访问地址

部署完成后，您可以通过以下地址访问：

- **管理面板**: `https://your-domain.com/admin`
- **API 接口**: `https://your-domain.com/api`
- **健康检查**: `https://your-domain.com/health`

## 初始设置

### 1. 创建管理员账户
1. 访问 `https://your-domain.com/admin`
2. 填写管理员信息创建账户
3. 登录管理面板

### 2. 配置 API 权限
1. 进入 "Settings" → "API Tokens"
2. 创建新的 API Token
3. 设置适当的权限

### 3. 配置内容类型权限
1. 进入 "Settings" → "Users & Permissions Plugin" → "Roles"
2. 编辑 "Public" 角色
3. 为需要的内容类型设置 "find" 和 "findOne" 权限

## 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查日志
pm2 logs trade11-cms

# 检查端口占用
netstat -tlnp | grep 1337

# 检查环境变量
cat .env
```

#### 2. 数据库连接失败
- 检查数据库服务是否运行
- 验证数据库用户名和密码
- 确认数据库名称正确

#### 3. 权限问题
```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/trade11-cms
chmod -R 755 /www/wwwroot/trade11-cms
```

#### 4. 内存不足
- 增加服务器内存
- 或在 `ecosystem.config.js` 中调整 `max_memory_restart`

### 日志位置
- **应用日志**: `/www/wwwroot/trade11-cms/logs/`
- **Nginx 日志**: `/www/wwwlogs/`
- **PM2 日志**: `~/.pm2/logs/`

## 性能优化

### 1. 数据库优化
- 定期清理日志表
- 添加适当的索引
- 配置数据库连接池

### 2. 缓存配置
- 启用 Redis 缓存
- 配置 CDN
- 启用 Gzip 压缩

### 3. 监控设置
- 配置 PM2 监控
- 设置日志轮转
- 配置告警通知

## 安全建议

1. **定期更新**: 保持 Strapi 和依赖包最新
2. **强密码**: 使用强密码和随机密钥
3. **防火墙**: 配置适当的防火墙规则
4. **备份**: 定期备份数据库和文件
5. **SSL**: 始终使用 HTTPS
6. **权限**: 最小化 API 权限

## 支持

如果遇到问题，请检查：
1. [Strapi 官方文档](https://docs.strapi.io/)
2. [宝塔面板文档](https://www.bt.cn/bbs/)
3. 项目日志文件
