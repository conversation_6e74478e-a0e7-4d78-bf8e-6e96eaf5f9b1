# 环境配置示例文件
# 复制此文件为 .env.local 并填入实际值

# ===========================================
# 基础配置
# ===========================================

# 应用环境 (development | production | test)
NODE_ENV=production

# 应用端口
PORT=3000

# 应用域名
NEXT_PUBLIC_APP_URL=https://your-domain.com

# 应用名称
NEXT_PUBLIC_APP_NAME="Admin Dashboard"

# API 基础地址
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001

# ===========================================
# Strapi CMS 配置
# ===========================================

# Strapi CMS 地址 (前端可访问)
NEXT_PUBLIC_STRAPI_URL=http://localhost:1337

# Strapi API Token (服务端使用)
STRAPI_API_TOKEN=your-strapi-api-token-here

# 生产环境 Strapi 配置
# NEXT_PUBLIC_STRAPI_URL=https://your-cms-domain.com
# STRAPI_API_TOKEN=your-production-api-token

# ===========================================
# 认证配置
# ===========================================

# JWT 密钥 (请使用强密码)
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# JWT 过期时间
JWT_EXPIRES_IN=7d

# 管理员默认密码 (首次部署后请修改)
ADMIN_DEFAULT_PASSWORD=admin123456

# ===========================================
# 数据库配置
# ===========================================

# MySQL 数据库配置
DATABASE_URL=your-database-url-here

# 或者分别配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database

# ===========================================
# 支付配置
# ===========================================

# PayPal 配置
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your-paypal-client-id-here
PAYPAL_CLIENT_SECRET=your-paypal-client-secret-here
PAYPAL_ENVIRONMENT=sandbox

# 微信支付配置
WECHAT_PAY_APP_ID=your-app-id
WECHAT_PAY_MCH_ID=your-mch-id
WECHAT_PAY_KEY=your-api-key

# 支付宝配置
ALIPAY_APP_ID=your-app-id
ALIPAY_PRIVATE_KEY=your-private-key

# ===========================================
# 第三方服务配置
# ===========================================

# 邮件服务配置 (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>

# 文件存储配置 (阿里云 OSS)
ALIYUN_OSS_REGION=oss-cn-hangzhou
ALIYUN_OSS_ACCESS_KEY_ID=your-access-key-id
ALIYUN_OSS_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_OSS_BUCKET=your-bucket-name

# Redis 配置 (缓存和会话)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# ===========================================
# 分析和监控
# ===========================================

# Google Analytics
NEXT_PUBLIC_GA_ID=GA-XXXXXXXXX

# Sentry 错误监控
SENTRY_DSN=https://<EMAIL>/project-id

# ===========================================
# 宝塔面板部署配置
# ===========================================

# 网站根目录
WEBSITE_ROOT=/www/wwwroot/your-domain.com

# 是否启用 HTTPS 重定向
FORCE_HTTPS=true

# 时区设置
TZ=Asia/Shanghai

# 语言设置
LOCALE=zh-CN
