# Trade11 项目集成指南

本指南将帮助您将 Next.js 前后端项目与 Strapi CMS 进行完整集成，实现统一的内容管理系统。

## 项目概述

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js 前端   │    │   Next.js 后端   │    │   Strapi CMS    │
│   (用户界面)     │◄──►│   (管理界面)     │◄──►│   (内容管理)     │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 1337    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流向
1. **内容管理**: 通过 Strapi CMS 管理所有内容
2. **数据同步**: Next.js 前后端通过 API 获取 Strapi 数据
3. **统一存储**: 所有数据存储在 Strapi 数据库中

## 快速开始

### 1. 环境准备

#### 系统要求
- Node.js 18+
- MySQL 5.7+ 或 PostgreSQL 12+
- 宝塔面板 (生产环境)

#### 安装依赖
```bash
# 安装 Next.js 项目依赖
cd next前后端
npm install

# 安装 Strapi CMS 依赖
cd ../trade11-cms
npm install
```

### 2. 配置环境变量

#### Next.js 项目配置
```bash
cd next前后端
cp .env.example .env.local
```

编辑 `.env.local`:
```env
# Strapi CMS 配置
NEXT_PUBLIC_STRAPI_URL=http://localhost:1337
STRAPI_API_TOKEN=your-strapi-api-token-here

# 其他配置...
```

#### Strapi CMS 配置
```bash
cd trade11-cms
cp .env.example .env
```

编辑 `.env`:
```env
# 基本配置
NODE_ENV=development
HOST=0.0.0.0
PORT=1337

# 数据库配置 (开发环境使用 SQLite)
DATABASE_CLIENT=sqlite
DATABASE_FILENAME=.tmp/data.db

# 生产环境使用 MySQL
# DATABASE_CLIENT=mysql
# DATABASE_HOST=localhost
# DATABASE_PORT=3306
# DATABASE_NAME=trade11_cms
# DATABASE_USERNAME=trade11_cms_user
# DATABASE_PASSWORD=your-password

# 安全密钥 (生产环境请使用随机字符串)
APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt-here
ADMIN_JWT_SECRET=your-admin-jwt-secret-here
TRANSFER_TOKEN_SALT=your-transfer-token-salt-here
JWT_SECRET=your-jwt-secret-here

# CORS 配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
```

### 3. 启动开发环境

#### 启动 Strapi CMS
```bash
cd trade11-cms
npm run develop
```

访问 `http://localhost:1337/admin` 创建管理员账户。

#### 启动 Next.js 项目
```bash
# 启动前端
cd next前后端
npm run dev

# 启动后端 (新终端)
cd next前后端
npm run dev:backend
```

## 内容类型配置

### 已创建的内容类型

1. **Product (产品)**
   - 产品信息、价格、分类、供应商等
   - 支持多图片、规格参数、SEO 配置

2. **Supplier (供应商)**
   - 供应商信息、联系方式、认证状态等
   - 关联产品管理

3. **Product Category (产品分类)**
   - 层级分类结构
   - 支持图标、图片、SEO 配置

4. **Customer (客户)**
   - 客户信息、VIP 等级、订阅状态等
   - 关联订单管理

5. **Order (订单)**
   - 订单信息、支付状态、物流跟踪等
   - 完整的订单生命周期管理

6. **Page Content (页面内容)**
   - 动态页面内容管理
   - 支持组件化内容结构

7. **Site Config (网站配置)**
   - 全局网站配置
   - 联系信息、社交媒体、主题设置等

### API 权限配置

在 Strapi 管理面板中配置 API 权限：

1. 进入 "Settings" → "Users & Permissions Plugin" → "Roles"
2. 编辑 "Public" 角色，设置以下权限：
   - **Product**: find, findOne
   - **Supplier**: find, findOne
   - **Product-category**: find, findOne
   - **Page-content**: find, findOne
   - **Site-config**: find

3. 创建 API Token：
   - 进入 "Settings" → "API Tokens"
   - 创建新 Token，设置适当权限
   - 复制 Token 到 Next.js 项目的环境变量中

## 数据集成

### 在 Next.js 中使用 Strapi 数据

#### 获取产品列表
```typescript
import { strapiAPI } from '@/lib/strapi/client';

// 获取所有产品
const products = await strapiAPI.products.getAll({
  populate: ['category', 'supplier', 'images'],
  sort: 'createdAt:desc',
  pagination: {
    page: 1,
    pageSize: 10
  }
});

// 获取特定产品
const product = await strapiAPI.products.getById(1, ['category', 'supplier']);
```

#### 获取页面内容
```typescript
// 获取首页内容
const homeContent = await strapiAPI.pageContent.getByPageType('home', ['sections']);

// 获取网站配置
const siteConfig = await strapiAPI.siteConfig.get(['logo', 'contactInfo']);
```

#### 在组件中使用
```tsx
'use client';

import { useEffect, useState } from 'react';
import { strapiAPI } from '@/lib/strapi/client';

export default function ProductList() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchProducts() {
      try {
        const response = await strapiAPI.products.getAll({
          populate: ['category', 'supplier']
        });
        setProducts(response.data);
      } catch (error) {
        console.error('Failed to fetch products:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchProducts();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      {products.map((product) => (
        <div key={product.id}>
          <h3>{product.attributes.name}</h3>
          <p>{product.attributes.description}</p>
          <p>Price: ${product.attributes.priceMin} - ${product.attributes.priceMax}</p>
        </div>
      ))}
    </div>
  );
}
```

## 生产环境部署

### 宝塔面板一键部署

#### 1. 部署 Strapi CMS
```bash
# 上传 trade11-cms 到服务器
cd /www/wwwroot/trade11-cms

# 运行一键部署脚本
chmod +x deploy-bt.sh
./deploy-bt.sh
```

#### 2. 配置数据库
1. 在宝塔面板创建 MySQL 数据库
2. 数据库名: `trade11_cms`
3. 用户名: `trade11_cms_user`
4. 更新 `.env` 文件中的数据库配置

#### 3. 配置域名和 SSL
1. 在宝塔面板添加网站
2. 配置域名解析
3. 申请 SSL 证书
4. 配置 Nginx 反向代理

#### 4. 部署 Next.js 项目
```bash
# 构建项目
cd /www/wwwroot/next前后端
npm run build

# 使用 PM2 启动
pm2 start ecosystem.config.js --env production
```

### 环境变量配置

#### 生产环境 Strapi 配置
```env
NODE_ENV=production
DATABASE_CLIENT=mysql
DATABASE_HOST=localhost
DATABASE_NAME=trade11_cms
DATABASE_USERNAME=trade11_cms_user
DATABASE_PASSWORD=your-secure-password
CORS_ORIGIN=https://your-frontend-domain.com,https://your-admin-domain.com
```

#### 生产环境 Next.js 配置
```env
NODE_ENV=production
NEXT_PUBLIC_STRAPI_URL=https://your-cms-domain.com
STRAPI_API_TOKEN=your-production-api-token
```

## 常见问题

### 1. CORS 错误
确保在 Strapi 的 `.env` 文件中正确配置 `CORS_ORIGIN`：
```env
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,https://your-domain.com
```

### 2. API Token 权限不足
在 Strapi 管理面板中检查 API Token 权限，确保有足够的访问权限。

### 3. 数据库连接失败
检查数据库配置和网络连接，确保数据库服务正常运行。

### 4. 文件上传问题
检查文件上传目录权限，确保 Strapi 有写入权限。

## 开发建议

### 1. 数据模型设计
- 保持数据模型的一致性
- 合理使用关联关系
- 添加必要的索引

### 2. API 使用
- 使用 populate 参数获取关联数据
- 合理使用分页和过滤
- 实现错误处理

### 3. 性能优化
- 启用缓存
- 优化数据库查询
- 使用 CDN 加速静态资源

### 4. 安全考虑
- 定期更新依赖包
- 使用强密码和随机密钥
- 配置适当的 CORS 策略
- 定期备份数据

## 支持

如果遇到问题，请查看：
1. [Strapi 官方文档](https://docs.strapi.io/)
2. [Next.js 官方文档](https://nextjs.org/docs)
3. 项目日志文件
4. 宝塔面板错误日志
