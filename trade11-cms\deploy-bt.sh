#!/bin/bash

# 宝塔面板一键部署脚本 - Trade11 CMS
# 使用方法: chmod +x deploy-bt.sh && ./deploy-bt.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="trade11-cms"
PROJECT_PATH="/www/wwwroot/${PROJECT_NAME}"
BACKUP_PATH="/www/backup/${PROJECT_NAME}"
NODE_VERSION="18"
PM2_APP_NAME="trade11-cms"

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到 root 用户，建议使用普通用户运行"
    fi
}

# 检查宝塔面板
check_bt_panel() {
    log_info "检查宝塔面板..."
    if ! command -v bt &> /dev/null; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    log_success "宝塔面板检查通过"
}

# 检查 Node.js 版本
check_nodejs() {
    log_info "检查 Node.js 版本..."
    if ! command -v node &> /dev/null; then
        log_error "未检测到 Node.js，请在宝塔面板软件商店安装 Node.js ${NODE_VERSION}"
        exit 1
    fi
    
    NODE_CURRENT=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_CURRENT" -lt "$NODE_VERSION" ]; then
        log_error "Node.js 版本过低 (当前: v${NODE_CURRENT}, 需要: v${NODE_VERSION}+)"
        exit 1
    fi
    log_success "Node.js 版本检查通过 (v${NODE_CURRENT})"
}

# 检查 PM2
check_pm2() {
    log_info "检查 PM2..."
    if ! command -v pm2 &> /dev/null; then
        log_info "安装 PM2..."
        npm install -g pm2
    fi
    log_success "PM2 检查通过"
}

# 检查数据库
check_database() {
    log_info "检查数据库配置..."
    
    # 检查 MySQL 是否运行
    if ! systemctl is-active --quiet mysql; then
        log_error "MySQL 服务未运行，请在宝塔面板启动 MySQL"
        exit 1
    fi
    
    log_success "数据库检查通过"
}

# 创建项目目录
create_directories() {
    log_info "创建项目目录..."
    
    # 创建主目录
    mkdir -p "$PROJECT_PATH"
    mkdir -p "$BACKUP_PATH"
    mkdir -p "$PROJECT_PATH/logs"
    
    # 设置权限
    chown -R www:www "$PROJECT_PATH"
    chmod -R 755 "$PROJECT_PATH"
    
    log_success "目录创建完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    cd "$PROJECT_PATH"
    
    if [ -f "package.json" ]; then
        npm install --production
        log_success "依赖安装完成"
    else
        log_error "未找到 package.json 文件"
        exit 1
    fi
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f "$PROJECT_PATH/.env" ]; then
        cat > "$PROJECT_PATH/.env" << EOF
# 基本配置
NODE_ENV=production
HOST=0.0.0.0
PORT=1337

# 安全密钥 (请修改为随机字符串)
APP_KEYS=$(openssl rand -base64 32),$(openssl rand -base64 32),$(openssl rand -base64 32),$(openssl rand -base64 32)
API_TOKEN_SALT=$(openssl rand -base64 32)
ADMIN_JWT_SECRET=$(openssl rand -base64 32)
TRANSFER_TOKEN_SALT=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 32)

# 数据库配置
DATABASE_CLIENT=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=trade11_cms
DATABASE_USERNAME=trade11_cms_user
DATABASE_PASSWORD=
DATABASE_SSL=false

# 文件上传
UPLOAD_PROVIDER=local
UPLOAD_SIZE_LIMIT=104857600

# CORS 配置
CORS_ENABLED=true
CORS_ORIGIN=*
EOF
        log_success "环境变量配置完成"
        log_warning "请编辑 .env 文件设置数据库密码和其他配置"
    else
        log_info "环境变量文件已存在，跳过创建"
    fi
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."
    cd "$PROJECT_PATH"
    
    # 运行数据库迁移
    npm run strapi build
    log_success "数据库初始化完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    cd "$PROJECT_PATH"
    
    # 停止现有服务
    pm2 delete "$PM2_APP_NAME" 2>/dev/null || true
    
    # 启动新服务
    pm2 start ecosystem.config.js --env production
    pm2 save
    pm2 startup
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    sleep 10
    
    if curl -f http://localhost:1337/admin > /dev/null 2>&1; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败，请检查服务状态"
        pm2 logs "$PM2_APP_NAME" --lines 20
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo ""
    echo "=== 部署信息 ==="
    echo "项目路径: $PROJECT_PATH"
    echo "管理面板: http://your-domain.com/admin"
    echo "API 地址: http://your-domain.com/api"
    echo ""
    echo "=== 常用命令 ==="
    echo "查看日志: pm2 logs $PM2_APP_NAME"
    echo "重启服务: pm2 restart $PM2_APP_NAME"
    echo "停止服务: pm2 stop $PM2_APP_NAME"
    echo "服务状态: pm2 status"
    echo ""
    echo "=== 下一步 ==="
    echo "1. 在宝塔面板创建网站并配置域名"
    echo "2. 配置 Nginx 反向代理到端口 1337"
    echo "3. 配置 SSL 证书"
    echo "4. 访问管理面板创建管理员账户"
    echo "5. 配置 Next.js 前端连接到此 CMS"
}

# 主函数
main() {
    log_info "开始部署 Trade11 CMS..."
    
    check_root
    check_bt_panel
    check_nodejs
    check_pm2
    check_database
    create_directories
    install_dependencies
    setup_environment
    init_database
    start_service
    health_check
    show_deployment_info
}

# 执行主函数
main "$@"
