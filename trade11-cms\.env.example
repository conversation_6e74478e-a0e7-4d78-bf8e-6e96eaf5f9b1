# 基本配置
NODE_ENV=production
HOST=0.0.0.0
PORT=1337

# 安全密钥 (生产环境请使用随机字符串)
APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt-here
ADMIN_JWT_SECRET=your-admin-jwt-secret-here
TRANSFER_TOKEN_SALT=your-transfer-token-salt-here
JWT_SECRET=your-jwt-secret-here

# 数据库配置
DATABASE_CLIENT=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=trade11_cms
DATABASE_USERNAME=trade11_cms_user
DATABASE_PASSWORD=your-database-password
DATABASE_SSL=false

# PostgreSQL 配置 (可选)
# DATABASE_CLIENT=postgres
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=trade11_cms
# DATABASE_USERNAME=trade11_cms_user
# DATABASE_PASSWORD=your-database-password
# DATABASE_SCHEMA=public
# DATABASE_SSL=false

# SQLite 配置 (开发环境)
# DATABASE_CLIENT=sqlite
# DATABASE_FILENAME=.tmp/data.db

# 文件上传配置
UPLOAD_PROVIDER=local
UPLOAD_SIZE_LIMIT=104857600

# CORS 配置
CORS_ENABLED=true
CORS_ORIGIN=http://localhost:3001,https://your-frontend-domain.com

# 邮件配置 (可选)
EMAIL_PROVIDER=sendmail
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# 监控和日志
LOG_LEVEL=info
LOG_QUERIES=false
LOG_REQUESTS=false

# 性能配置
BODY_PARSER_JSON_LIMIT=2mb
BODY_PARSER_FORM_LIMIT=2mb

# 安全配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX=100
RATE_LIMIT_DURATION=60000

# 管理面板配置
ADMIN_PATH=/admin
ADMIN_AUTOOPEN=false

# API 配置
API_PREFIX=/api

# 开发配置
STRAPI_DISABLE_UPDATE_NOTIFICATION=true
STRAPI_HIDE_STARTUP_MESSAGE=false
